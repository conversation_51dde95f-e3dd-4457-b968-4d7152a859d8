// In BACKEND/main.go
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/cors"
)

type App struct {
	DB *pgxpool.Pool
}

type ImportRequest struct {
	Filename string `json:"filename"`
	SymbolID int    `json:"symbol_id"`
}

func (a *App) importHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}

	var req ImportRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	filePath := filepath.Join("/DATA", filepath.Base(req.Filename))
	log.Printf("Processing import for symbol %d from file: %s", req.SymbolID, filePath)

	// --- THE FIX IS HERE: Use 'CALL' to execute a PROCEDURE and 'Exec' to run it ---
	// A procedure does not return a value, so we use Exec instead of QueryRow.
	_, err := a.DB.Exec(context.Background(), "CALL process_tick_file($1, $2)", filePath, req.SymbolID)
	if err != nil {
		http.Error(w, "Database processing failed", http.StatusInternalServerError)
		log.Printf("Error calling process_tick_file: %v", err)
		return
	}

	successMessage := fmt.Sprintf("Successfully submitted file %s for processing for symbol %d.", req.Filename, req.SymbolID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": successMessage})
}

// Placeholder for the backtesting handler
func (a *App) dataHandler(w http.ResponseWriter, r *http.Request) {
	fmt.Fprint(w, "Backtesting endpoint not yet implemented.")
}

func main() {
	databaseUrl := os.Getenv("DATABASE_URL")
	if databaseUrl == "" {
		log.Fatal("DATABASE_URL environment variable is not set.")
	}

	dbpool, err := pgxpool.New(context.Background(), databaseUrl)
	if err != nil {
		log.Fatalf("Unable to create connection pool: %v\n", err)
	}
	defer dbpool.Close()

	app := &App{DB: dbpool}

	mux := http.NewServeMux()
	mux.HandleFunc("/api/import/ticks", app.importHandler)
	mux.HandleFunc("/api/data", app.dataHandler) // Placeholder

	handler := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST"},
	}).Handler(mux)

	fmt.Println("Starting Continuum API server on :8080")
	if err := http.ListenAndServe(":8080", handler); err != nil {
		log.Fatalf("Could not start server: %s\n", err)
	}
}
