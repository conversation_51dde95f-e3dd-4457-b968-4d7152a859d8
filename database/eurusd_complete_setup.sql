-- =========== EURUSD COMPLETE SETUP SCRIPT ===========
-- PURPOSE: Create database with correct structure and import EURUSD data
-- COLUMNS: UTC, Symbol, Ask, Bid, AskVol, BidVol (Symbol=1 for EURUSD)
-- CONTINUOUS AGGREGATES: 10s, 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d
-- RUN TIME: ~30-45 minutes

-- =========== STEP 1: CREATE DATABASE ===========
\c postgres
\set AUTOCOMMIT on

-- Terminate existing connections and recreate database
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'mktdata' AND pid <> pg_backend_pid();

DROP DATABASE IF EXISTS mktdata;
CREATE DATABASE mktdata;

\echo 'SUCCESS: Database created'

-- =========== STEP 2: SETUP TABLES ===========
\c mktdata
\set AUTOCOMMIT off

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create main ticks table with EXACT column structure you want
CREATE TABLE ticks (
    utc TIMESTAMPTZ NOT NULL,
    symbol INTEGER NOT NULL,
    ask DOUBLE PRECISION NOT NULL,
    bid DOUBLE PRECISION NOT NULL,
    askvol REAL,
    bidvol REAL,
    CONSTRAINT ticks_symbol_utc_unique UNIQUE (symbol, utc)
);

-- Create staging table for imports
CREATE TABLE ticks_staging (
    utc TIMESTAMPTZ,
    symbol INTEGER,
    ask DOUBLE PRECISION,
    bid DOUBLE PRECISION,
    askvol REAL,
    bidvol REAL
);

-- Convert to hypertable (7-day chunks, partitioned by symbol)
SELECT create_hypertable('ticks', 'utc', 'symbol', 4, chunk_time_interval => INTERVAL '7 days');

\echo 'SUCCESS: Tables created and hypertable configured'

-- =========== STEP 3: CREATE IMPORT FUNCTION ===========
CREATE OR REPLACE FUNCTION import_eurusd_file(file_path TEXT) RETURNS INTEGER
LANGUAGE plpgsql AS $$
DECLARE
    rows_imported INTEGER;
    rows_inserted INTEGER;
BEGIN
    -- Clear staging table
    TRUNCATE ticks_staging;
    
    -- Import CSV data (assumes CSV has: UTC, Ask, Bid, AskVol, BidVol columns)
    EXECUTE format('COPY ticks_staging (utc, ask, bid, askvol, bidvol) FROM %L WITH (FORMAT CSV, HEADER)', file_path);
    GET DIAGNOSTICS rows_imported = ROW_COUNT;
    
    -- Set symbol to 1 for EURUSD on all imported rows
    UPDATE ticks_staging SET symbol = 1 WHERE symbol IS NULL;
    
    -- Insert into main table (ignore duplicates based on symbol+utc constraint)
    INSERT INTO ticks (utc, symbol, ask, bid, askvol, bidvol)
    SELECT utc, symbol, ask, bid, askvol, bidvol 
    FROM ticks_staging 
    ON CONFLICT (symbol, utc) DO NOTHING;
    
    GET DIAGNOSTICS rows_inserted = ROW_COUNT;
    
    -- Clear staging table
    TRUNCATE ticks_staging;
    
    RAISE NOTICE 'File: % | Imported: % rows | Inserted: % rows', file_path, rows_imported, rows_inserted;
    
    RETURN rows_inserted;
END;
$$;

\echo 'SUCCESS: Import function created'

-- =========== STEP 4: IMPORT EURUSD DATA ===========
\echo 'Starting EURUSD data import...'

\echo 'Importing EURUSD historical data (2019-2022)...'
SELECT import_eurusd_file('/tmp/EURUSD_Ticks_2019.12.31_2022.01.01.csv') as historical_rows;

\echo 'Importing EURUSD main dataset...'
SELECT import_eurusd_file('/tmp/EURUSD_Ticks.csv') as main_rows;

\echo 'Importing EURUSD recent data (2025)...'
SELECT import_eurusd_file('/tmp/EURUSD_Ticks_2025.07.01_2025.07.15.csv') as recent_rows;

-- Commit all data imports
COMMIT;

\echo 'SUCCESS: All EURUSD data imported and committed'

-- =========== STEP 5: CREATE ALL 9 CONTINUOUS AGGREGATES ===========
\echo 'Creating continuous aggregates...'

-- 10 second OHLC
CREATE MATERIALIZED VIEW ohlc_10s
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('10 seconds', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 1 minute OHLC
CREATE MATERIALIZED VIEW ohlc_1m
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('1 minute', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 5 minute OHLC
CREATE MATERIALIZED VIEW ohlc_5m
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('5 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 10 minute OHLC
CREATE MATERIALIZED VIEW ohlc_10m
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('10 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 15 minute OHLC
CREATE MATERIALIZED VIEW ohlc_15m
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('15 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 30 minute OHLC
CREATE MATERIALIZED VIEW ohlc_30m
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('30 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 1 hour OHLC
CREATE MATERIALIZED VIEW ohlc_1h
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('1 hour', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 4 hour OHLC
CREATE MATERIALIZED VIEW ohlc_4h
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('4 hours', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 1 day OHLC
CREATE MATERIALIZED VIEW ohlc_1d
WITH (timescaledb.continuous) AS
SELECT symbol,
       time_bucket('1 day', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

\echo 'SUCCESS: All 9 continuous aggregates created'

-- =========== STEP 6: VERIFICATION ===========
\echo 'Verifying setup...'

-- Show data summary
\echo 'EURUSD Data Summary:'
SELECT
    'EURUSD' as currency_pair,
    symbol,
    COUNT(*) as total_rows,
    MIN(utc) as earliest_date,
    MAX(utc) as latest_date,
    MIN(ask) as min_ask,
    MAX(ask) as max_ask,
    MIN(bid) as min_bid,
    MAX(bid) as max_bid
FROM ticks
WHERE symbol = 1
GROUP BY symbol;

-- Show continuous aggregates created
\echo 'Continuous Aggregates Created:'
SELECT schemaname, matviewname as aggregate_name
FROM pg_matviews
WHERE matviewname LIKE 'ohlc_%'
ORDER BY matviewname;

-- Test sample data from each timeframe
\echo 'Sample 1-minute OHLC data (latest 3 bars):'
SELECT bucket, open_ask, high_ask, low_ask, close_ask, tick_count
FROM ohlc_1m
WHERE symbol = 1
ORDER BY bucket DESC
LIMIT 3;

\echo 'Sample 1-hour OHLC data (latest 3 bars):'
SELECT bucket, open_ask, high_ask, low_ask, close_ask, tick_count
FROM ohlc_1h
WHERE symbol = 1
ORDER BY bucket DESC
LIMIT 3;

\echo '========================================='
\echo 'SETUP COMPLETE!'
\echo 'Database: mktdata'
\echo 'Table: ticks (UTC, Symbol, Ask, Bid, AskVol, BidVol)'
\echo 'EURUSD Symbol: 1'
\echo 'Continuous Aggregates: 10s, 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d'
\echo 'Ready for trading strategy development!'
\echo '========================================='
