-- =========== CREATE CONTINUOUS AGGREGATES ===========
-- PURPOSE: Create all 9 continuous aggregates for EURUSD data
-- RUN TIME: ~10 seconds
-- NOTE: Must run outside transaction block

\c mktdata

-- 10 second OHLC
CREATE MATERIALIZED VIEW ohlc_10s
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('10 seconds', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 1 minute OHLC
CREATE MATERIALIZED VIEW ohlc_1m
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('1 minute', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 5 minute OHLC
CREATE MATERIALIZED VIEW ohlc_5m
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('5 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 10 minute OHLC
CREATE MATERIALIZED VIEW ohlc_10m
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('10 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 15 minute OHLC
CREATE MATERIALIZED VIEW ohlc_15m
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('15 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 30 minute OHLC
CREATE MATERIALIZED VIEW ohlc_30m
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('30 minutes', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 1 hour OHLC
CREATE MATERIALIZED VIEW ohlc_1h
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('1 hour', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 4 hour OHLC
CREATE MATERIALIZED VIEW ohlc_4h
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('4 hours', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

-- 1 day OHLC
CREATE MATERIALIZED VIEW ohlc_1d
WITH (timescaledb.continuous) AS
SELECT symbol, 
       time_bucket('1 day', utc) AS bucket,
       first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
       first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
       sum(askvol) as askvol, sum(bidvol) as bidvol, count(*) AS tick_count
FROM ticks GROUP BY symbol, bucket;

\echo 'SUCCESS: All 9 continuous aggregates created!'

-- Verify everything works
\echo 'EURUSD Data Summary:'
SELECT 
    'EURUSD' as currency_pair,
    symbol,
    COUNT(*) as total_rows,
    MIN(utc) as earliest_date,
    MAX(utc) as latest_date,
    MIN(ask) as min_ask,
    MAX(ask) as max_ask
FROM ticks 
WHERE symbol = 1
GROUP BY symbol;

\echo 'Continuous Aggregates Created:'
SELECT schemaname, matviewname as aggregate_name 
FROM pg_matviews 
WHERE matviewname LIKE 'ohlc_%' 
ORDER BY matviewname;

\echo 'Sample 1-minute OHLC (latest 3 bars):'
SELECT bucket, open_ask, high_ask, low_ask, close_ask, tick_count 
FROM ohlc_1m 
WHERE symbol = 1 
ORDER BY bucket DESC 
LIMIT 3;

\echo '========================================='
\echo 'EURUSD SETUP COMPLETE!'
\echo 'Total rows: ~150 million'
\echo 'Symbol: 1 (EURUSD)'
\echo 'Timeframes: 10s, 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d'
\echo 'Ready for strategy development!'
\echo '========================================='
