-- =========== VERIFICATION AND PERFORMANCE TEST ===========
\c mktdata

\echo '========== TABLE STRUCTURE =========='
\d ticks

\echo ''
\echo '========== SAMPLE TICK DATA (First 3 rows) =========='
SELECT utc, symbol, ask, bid, askvol, bidvol 
FROM ticks 
WHERE symbol = 1 
ORDER BY utc 
LIMIT 3;

\echo ''
\echo '========== CONTINUOUS AGGREGATES CREATED =========='
SELECT schemaname, matviewname as aggregate_name 
FROM pg_matviews 
WHERE matviewname LIKE 'ohlc_%' 
ORDER BY matviewname;

\echo ''
\echo '========== PERFORMANCE TEST: COUNT QUERIES WITH TIMING =========='

\timing on

\echo 'Counting total ticks...'
SELECT COUNT(*) as total_ticks FROM ticks WHERE symbol = 1;

\echo 'Counting 10-second buckets...'
SELECT COUNT(*) as buckets_10s FROM ohlc_10s WHERE symbol = 1;

\echo 'Counting 1-minute buckets...'
SELECT COUNT(*) as buckets_1m FROM ohlc_1m WHERE symbol = 1;

\echo 'Counting 5-minute buckets...'
SELECT COUNT(*) as buckets_5m FROM ohlc_5m WHERE symbol = 1;

\echo 'Counting 10-minute buckets...'
SELECT COUNT(*) as buckets_10m FROM ohlc_10m WHERE symbol = 1;

\echo 'Counting 15-minute buckets...'
SELECT COUNT(*) as buckets_15m FROM ohlc_15m WHERE symbol = 1;

\echo 'Counting 30-minute buckets...'
SELECT COUNT(*) as buckets_30m FROM ohlc_30m WHERE symbol = 1;

\echo 'Counting 1-hour buckets...'
SELECT COUNT(*) as buckets_1h FROM ohlc_1h WHERE symbol = 1;

\echo 'Counting 4-hour buckets...'
SELECT COUNT(*) as buckets_4h FROM ohlc_4h WHERE symbol = 1;

\echo 'Counting 1-day buckets...'
SELECT COUNT(*) as buckets_1d FROM ohlc_1d WHERE symbol = 1;

\timing off

\echo ''
\echo '========== SAMPLE OHLC DATA =========='
\echo 'Latest 3 x 1-minute bars:'
SELECT bucket, open_ask, high_ask, low_ask, close_ask, open_bid, high_bid, low_bid, close_bid, tick_count 
FROM ohlc_1m 
WHERE symbol = 1 
ORDER BY bucket DESC 
LIMIT 3;

\echo ''
\echo 'Latest 3 x 1-hour bars:'
SELECT bucket, open_ask, high_ask, low_ask, close_ask, open_bid, high_bid, low_bid, close_bid, tick_count 
FROM ohlc_1h 
WHERE symbol = 1 
ORDER BY bucket DESC 
LIMIT 3;

\echo ''
\echo '========== DATA RANGE SUMMARY =========='
SELECT 
    'EURUSD' as currency_pair,
    symbol,
    COUNT(*) as total_rows,
    MIN(utc) as earliest_date,
    MAX(utc) as latest_date,
    MIN(ask) as min_ask,
    MAX(ask) as max_ask,
    MIN(bid) as min_bid,
    MAX(bid) as max_bid
FROM ticks 
WHERE symbol = 1
GROUP BY symbol;

\echo ''
\echo '========================================='
\echo 'VERIFICATION COMPLETE!'
\echo 'TimescaleDB setup is working perfectly!'
\echo '========================================='
