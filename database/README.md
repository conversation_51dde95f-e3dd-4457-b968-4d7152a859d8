# Database Scripts

## Working Scripts (Tested & Verified)

### `eurusd_complete_setup.sql`
**Purpose**: Complete EURUSD database setup and data import
**Runtime**: ~30-35 minutes
**What it does**:
1. Creates fresh `mktdata` database
2. Sets up TimescaleDB with correct table structure
3. Imports all 3 EURUSD CSV files with Symbol=1
4. Creates hypertable with 7-day chunks
5. Includes data verification

**Usage**:
```bash
psql -U postgres -f database/eurusd_complete_setup.sql
```

### `create_continuous_aggregates.sql`
**Purpose**: Creates all 9 continuous aggregates (10s, 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d)
**Runtime**: ~2-5 minutes (depends on data volume)
**Prerequisites**: Database and ticks table must exist

**Usage**:
```bash
psql -U postgres -f database/create_continuous_aggregates.sql
```

### `verify_setup.sql`
**Purpose**: Comprehensive verification and performance testing
**Runtime**: ~30 seconds
**What it shows**:
- Table structure
- Sample data
- Performance benchmarks for all timeframes
- Data range summary

**Usage**:
```bash
psql -U postgres -f database/verify_setup.sql
```

## Data Import Pattern for Additional Currency Pairs

Based on successful EURUSD import, the pattern for additional currencies:

1. **Modify** `eurusd_complete_setup.sql` to change:
   - File paths (e.g., `/tmp/GBPUSD_*.csv`)
   - Symbol ID (e.g., GBPUSD=3, USDJPY=2, USDCHF=4)
   - Function name (e.g., `import_gbpusd_file()`)

2. **Keep same structure**:
   - UTC, Symbol, Ask, Bid, AskVol, BidVol columns
   - Same continuous aggregates
   - Same verification queries

## Symbol Mapping
- **EURUSD**: 1 ✅ (Complete)
- **USDJPY**: 2 (Planned)
- **GBPUSD**: 3 (Planned)  
- **USDCHF**: 4 (Planned)

## Performance Reference
Based on EURUSD (150M rows):
- Import time: ~30 minutes
- Continuous aggregate creation: ~5 minutes
- Query performance: 15ms for 1-hour aggregates vs 2+ seconds for raw ticks
